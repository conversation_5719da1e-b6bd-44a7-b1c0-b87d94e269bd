import 'package:flutter/material.dart';

/// App colors based on the Algerian flag
/// Green: #006233 (dark green from flag)
/// White: #FFFFFF (white from flag)
/// Red: #D21034 (red from flag)
class AppColors {
  // Primary Algerian flag colors
  static const Color algerianGreen = Color(0xFF006233);
  static const Color algerianRed = Color(0xFFD21034);
  static const Color algerianWhite = Color(0xFFFFFFFF);
  
  // Variations for better UI design
  static const Color lightGreen = Color(0xFF4CAF50);
  static const Color darkGreen = Color(0xFF2E7D32);
  static const Color lightRed = Color(0xFFE57373);
  static const Color darkRed = Color(0xFFC62828);
  
  // Neutral colors
  static const Color backgroundLight = Color(0xFFF5F5F5);
  static const Color backgroundDark = Color(0xFF121212);
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textLight = Color(0xFFFFFFFF);
  
  // Accent colors for cultural elements
  static const Color tealAccent = Color(0xFF009688);
  static const Color sandBeige = Color(0xFFF5DEB3);
  static const Color desertOrange = Color(0xFFFF8C00);
  static const Color purpleAccent = Color(0xFF9C27B0);
  static const Color indigoAccent = Color(0xFF3F51B5);
  static const Color goldAccent = Color(0xFFFFD700);
  
  // Semantic colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
}
